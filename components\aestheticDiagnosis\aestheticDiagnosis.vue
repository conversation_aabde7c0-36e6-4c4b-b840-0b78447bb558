<template>

  <view class="w-full max-w-[750rpx] min-h-screen flex flex-col items-center justify-between mx-auto pt-0">
    <!-- 上半部分：图片展示 -->

    <view style="transition: all 0.3s;" class="w-full flex-1 flex flex-col  mt-24">
      <img ref="beforeimgRef" v-if="!generate.length" :src="props.peopleImg" class="w-full object-cover"
        mode="aspectFill">
      <ImgCompare v-else :before="props.peopleImg" :after="generate" :height="1000" />
      <template v-if="props.modelValue">
        <!-- 标签标记 -->
        <view class="absolute left-[25%] top-[30%]">
          <view class="tag">太阳穴凹陷</view>
        </view>
        <view class="absolute left-[45%] top-[20%]">
          <view class="tag">眉骨凸显</view>
        </view>
        <view class="absolute left-[45%] top-[35%]">
          <view class="tag">眼距过大</view>
        </view>
        <view class="absolute left-[25%] top-[45%]">
          <view class="tag">去卧蚕</view>
        </view>

        <!-- 下载按钮 -->
        <button class="absolute bottom-4 right-4 w-8 h-8 text-gray-600 text-center p-0"
          style="line-height: 2rem;border-radius: 50%;">
          <!-- <uni-icons type="download" size="24" color="#666"></uni-icons> -->
        </button>
      </template>

    </view>
    <view v-if="!!props.activeReport" class="popup-container"
      :class="[popupVisible ? 'slide-up' : 'slide-down', isExpanded ? 'expanded' : 'collapsed']" @click.stop>
      <!-- <view class="popup-header">
          <text class="popup-title"></text>
          <uni-icons type="closeempty" size="24" color="#666" @click="closePopup"></uni-icons>
        </view> -->

      <view v-if="props.activeReport == '诊断报告'" class=" " >
        <button @click="handlePreview()" style="margin: 0 auto;padding: 0;line-height: 30rpx;"
          class="bg-[#FF8F9C] text-white text-xs rounded  w-10 py-1 font-medium flex justify-center">
          <up-icon v-if="!isExpanded" size="20" color="#fff" name="arrow-up"></up-icon>
          <up-icon v-else size="20" color="#fff" name="arrow-down"></up-icon>
        </button>
        <view class="popup-content py-1 px-6 m-2 " v-if="'defect_report' in face_aes">
          <text class="text-sm mb-1 block" style="font-size: 28rpx;font-weight: 500;">下巴</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.chin.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">轮廓</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.contour.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">眼睛</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.eyes.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">额头</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.forehead.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">嘴巴</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.mouth.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">鼻子</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.nose.description }}
          </text>
          <view>
            您的总体评分：<view class="ml-4" style="display: inline-flex;">
             {{ face_aes.overall_score }}
            </view>
          </view>
        </view>
      </view>
      <template v-if="props.activeReport == '美学方案'">
        <button @click="handlePreview()" style="margin: 0 auto;padding: 0;line-height: 30rpx;"
          class="bg-[#FF8F9C] text-white text-xs rounded  w-10 py-1 font-medium flex justify-center">
          <up-icon v-if="!isExpanded" size="20" color="#fff" name="arrow-up"></up-icon>
          <up-icon v-else size="20" color="#fff" name="arrow-down"></up-icon>
        </button>
        <view class="popup-content py-1 px-6 m-2 ">
          <template v-for="(item, index) in face_aes.treatment_plans.plan_a.projects">
            <text class="text-sm mb-1 block" style="font-size: 28rpx;font-weight: 500;">{{item.name}}</text>
            <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
              {{ item.reason }}
            </text>
          </template>
          <text class="text-sm mb-1 block" style="font-size: 28rpx;font-weight: 500;">总结：</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
              {{ face_aes.treatment_plans.plan_a.reason }}
            </text>
        </view>
      </template>

      <scroll-view v-if="props.activeReport == '专属推荐'" class="popup-content" scroll-x style="white-space: nowrap;">
        <view class="flex flex-nowrap" style="width: max-content;">
          <view class="flex items-center mx-2 p-2 bg-[#F8F8F8]" v-for="(item, index) in recommendedItems"
            @click="gotoOrgList" :key="index">
            <div class="flex flex-col flex-1">
              <div class="text-[14px] text-[#222] leading-tight" style="font-family: 'PingFang SC', Arial;">{{
                item.title }}</div>
              <div class="text-[12px] text-[#999] mt-1" style="font-family: 'PingFang SC', Arial;">{{
                item.category }}
                <span>{{ item.location }}</span>
              </div>
            </div>

            <img :src="item.imgSrc" :alt="item.alt" class="w-14 h-14 rounded-lg object-cover ml-3" />
          </view>
        </view>
      </scroll-view>

    </view>

    <!-- 底部导航栏2 -->
    <view v-show="props.activeReport" style="z-index: 5;"
      class="fixed bottom-0 left-0 w-full bg-[#fff] border-t border-[#f0f0f0] transition-transform duration-300 transform"
      :class="{ 'translate-y-0': props.activeReport, 'translate-y-full': !props.activeReport }">
      <view class="flex justify-around items-center h-16 px-4">
        <view v-for="item in reports" style="font-size: 28rpx;border-color: #F39196;" class="font-semi"
          :class="props.activeReport == item ? 'font-semibold border-b-2' : ''" @click="selectIcon2(item)">{{ item
          }}</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import ImgCompare from '@/components/imgCompare.vue';
import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
import { callAiPolling } from "@/Api/index.js"
const props = defineProps({
  compareFlag: {
    type: Boolean,
    default: false
  },
  peopleImg: {
    type: String,
    default: ''
  },

  buttonIndex: {
    type: Number,
    default: -1
  },
  activeReport: {
    type: String,
    default: ''
  },
  icons: {
    type: Array,
    default: []
  },
  reports: {
    type: Array,
    default: []
  },

});
let timer = null

onMounted(() => {
  let label = props.icons[0].reports[0];
  selectIcon2(label);
  let operationId = uni.getStorageSync('operationId')
  if (operationId) {
    getStatus(operationId)

    timer = setInterval(async () => {
      getStatus(operationId)
    }, 3000)
  }
})
onBeforeUnmount(() => {

  clearInterval(timer)
})
const emit = defineEmits(['update:activeReport', 'update:loading', 'update:percent']);

const beforeImgHeight = ref(0) // 原图高度
const beforeimgRef = ref(null) // 图片引用
const isExpanded = ref(false) // 控制弹窗展开状态
const handlePreview = () => {
  if (beforeimgRef.value) {
    beforeImgHeight.value = beforeimgRef.value.height
  }
  isExpanded.value = !isExpanded.value
}

async function getStatus(operationId) {

  let { data } = await callAiPolling({ operationId })

  // 计算进度：aiStatus为'1'的数量 / 总数量 * 100
  const completedCount = data.data.filter(item => item.aiStatus == '1').length
  const totalCount = data.data.length
  const percent = Math.round((completedCount / totalCount) * 100)

  // 更新进度
  emit('update:percent', percent)

  let status = data.data.every(item => item.aiStatus == '1')
  if (status) {
    clearInterval(timer)
    // 完成后隐藏loading
    emit('update:loading', false)
    emit('update:percent', 100)
    organizeData(data.data)
  }
}
let face_aes = ref({})
let detect = ref({})
let generate = ref({})
function organizeData(data){
  
  face_aes.value = JSON.parse(data[0].aiResult)
  detect.value = JSON.parse(data[1].aiResult)
  generate.value =data[2].aiResult
  console.log(generate.value);
  
}
const popupVisible = ref(false)
const selectIcon2 = (item) => {
  // activeReport.value = item;
  emit('update:activeReport', item)
  setTimeout(() => {
    popupVisible.value = true;
  }, 50);
}

const gotoOrgList = () => {
  uni.navigateTo({
    url: '/pages/orgList/index'
  })
}
// 推荐项目数据
const recommendedItems = ref([
  {
    imgSrc: '/static/imgs/ject1.jpg',
    alt: "Side profile of a young woman, neutral expression, white background",
    title: "明星医师方案",
    category: "玻尿酸+肉毒素+光子嫩肤",
  },
  {
    imgSrc: '/static/imgs/ject2.jpg',
    alt: "Side profile of a young woman, neutral expression, white background",
    title: "黄金标准方案",
    category: "玻尿酸+肉毒素",
  },
  {
    imgSrc: '/static/imgs/ject3.jpg',
    alt: "Side profile of a young woman, neutral expression, white background",
    title: "经典臻选方案",
    category: "玻尿酸",
  },
]);



watch(() => props.activeReport, (newVal, oldVal) => {
}, { deep: true });
</script>

<style lang="scss" scoped>
/* 标签样式 */
.tag {
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

/* 弹窗样式 */
.popup-overlay {

  width: 100%;
  height: 100%;
  // background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: flex-end;
}

.popup-container {
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 4;
  width: 100%;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
  border-top-left-radius: 5rpx;
  border-top-right-radius: 5rpx;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 110rpx;
  overflow: hidden;
  will-change: transform, height;
  &>view{
    height: 100%;
    display: flex;
    flex-direction: column;
    .popup-content{
      flex: 1;
      overflow-y: auto;
    }
  }
}

.popup-container.collapsed {
  height: 200rpx;
  overflow: hidden;
}
.collapsed{
  .popup-content{
    overflow: hidden!important;
  }
}
.popup-container.expanded {
  max-height:60vh;
  height: fit-content;
  overflow-y: auto;
  min-height: 0;
}

.popup-container.slide-up {
  transform: translateY(0);
}

.popup-container.slide-down {
  transform: translateY(100%);
}

.popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
}

.popup-content {
  width: 100%;
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  transition: none; /* 移除内容区域的过渡动画 */
}

.popup-container.expanded .popup-content {
  overflow-y: auto;
}

.popup-container.collapsed .popup-content {
  overflow-y: auto;
}

/* 横向滚动样式 */
scroll-view ::v-deep {
  .flex {
    flex-wrap: nowrap;
  }
}

/* 底部标签栏样式 */
.bottom-tab {
  position: relative;
}

.bottom-tab::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  height: 4rpx;
  background-color: #ff4d4f;
}

.inactive-tab {
  color: #999;
}
</style>